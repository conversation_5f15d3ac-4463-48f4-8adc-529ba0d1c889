"""Chat and message entities for the FastAPI application."""

import uuid
from datetime import UTC, datetime
from typing import List
from uuid import UUID

from sqlalchemy import Column, DateTime, ForeignKey, String, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, relationship

from .base import BaseEntity


class Chat(BaseEntity):
    """Chat session entity."""

    __tablename__ = "chats"

    id: Mapped[UUID] = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title: Mapped[str] = Column(String(255), nullable=False, default="New Chat")
    user_id: Mapped[UUID] = Column(PostgresUUID(as_uuid=True), nullable=False)  # Remove foreign key for now
    is_active: Mapped[bool] = Column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), nullable=False)
    updated_at: Mapped[datetime] = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC), nullable=False)

    # Relationships
    messages: Mapped[List["Message"]] = relationship("Message", back_populates="chat", cascade="all, delete-orphan")
    # Remove the user relationship for now to avoid circular import issues
    # user: Mapped["Account"] = relationship("Account", back_populates="chats")

    def __repr__(self) -> str:
        return f"<Chat(id={self.id}, title='{self.title}', user_id={self.user_id})>"

    @classmethod
    def get_by_id(cls, session, chat_id: UUID, user_id: UUID = None):
        """Get chat by ID with optional user filter."""
        query = session.query(cls).filter(cls.id == chat_id, cls.is_active == True)
        if user_id:
            query = query.filter(cls.user_id == user_id)
        return query.first()

    @classmethod
    def get_user_chats(cls, session, user_id: UUID, limit: int = 50, offset: int = 0):
        """Get all chats for a user."""
        return (
            session.query(cls)
            .filter(cls.user_id == user_id, cls.is_active == True)
            .order_by(cls.updated_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    def update_title(self, new_title: str) -> None:
        """Update chat title."""
        self.title = new_title
        self.updated_at = datetime.now(UTC)

    def deactivate(self) -> None:
        """Deactivate chat (soft delete)."""
        self.is_active = False
        self.updated_at = datetime.now(UTC)


class Message(BaseEntity):
    """Chat message entity."""

    __tablename__ = "messages"

    id: Mapped[UUID] = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id: Mapped[UUID] = Column(PostgresUUID(as_uuid=True), ForeignKey("chats.id"), nullable=False)
    role: Mapped[str] = Column(String(50), nullable=False)  # 'user' or 'assistant'
    content: Mapped[str] = Column(Text, nullable=False)
    tokens_used: Mapped[int] = Column(String(20), nullable=True)  # Store as string for flexibility
    llm_model: Mapped[str] = Column(String(100), nullable=True)
    created_at: Mapped[datetime] = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), nullable=False)

    # Relationships
    chat: Mapped["Chat"] = relationship("Chat", back_populates="messages")

    def __repr__(self) -> str:
        return f"<Message(id={self.id}, role='{self.role}', chat_id={self.chat_id})>"

    @classmethod
    def get_chat_messages(cls, session, chat_id: UUID, limit: int = 100, offset: int = 0):
        """Get messages for a specific chat."""
        return (
            session.query(cls)
            .filter(cls.chat_id == chat_id)
            .order_by(cls.created_at.asc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    @classmethod
    def get_chat_messages_count(cls, session, chat_id: UUID) -> int:
        """Get total message count for a chat."""
        return session.query(cls).filter(cls.chat_id == chat_id).count() 