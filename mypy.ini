[mypy]
warn_return_any = True
warn_unused_configs = True
check_untyped_defs = True
cache_fine_grained = True
sqlite_cache = True

exclude = (?x)(
    alembic/
    | tests/
    | migrations/
  )

# Per-module configuration for FastAPI ecosystem
[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-src.services.database_example]
ignore_errors = True

[mypy-src.routes.cbv]
ignore_errors = True

[mypy-src.routes.base_router]
ignore_errors = True

[mypy-src.extensions.ext_security]
ignore_errors = True

[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-sqlmodel.*]
ignore_missing_imports = True

[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-starlette.*]
ignore_missing_imports = True

[mypy-uvicorn.*]
ignore_missing_imports = True

# Database and ORM related
[mypy-psycopg.*]
ignore_missing_imports = True

[mypy-alembic.*]
ignore_missing_imports = True

# Security and middleware
[mypy-passlib.*]
ignore_missing_imports = True

[mypy-jose.*]
ignore_missing_imports = True

[mypy-cryptography.*]
ignore_missing_imports = True

# Utilities
[mypy-dotenv.*]
ignore_missing_imports = True

[mypy-click.*]
ignore_missing_imports = True

# Testing
[mypy-pytest.*]
ignore_missing_imports = True

[mypy-httpx.*]
ignore_missing_imports = True
