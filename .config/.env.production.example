# Production Environment Configuration Example
# Copy this to .env for production deployment

# CRITICAL: Generate a strong secret key for production
SECRET_KEY=your-strong-secret-key-here-minimum-32-characters

# Production Environment Settings
APP_NAME="Madcrow-API-Server"
APP_VERSION=0.0.1
APP_INSTRUCTION="Production API server for Madcrow."

# IMPORTANT: Set to production mode
DEBUG=false
DEPLOY_ENV=PRODUCTION

# Server Configuration
BACKEND_APP_BIND_ADDRESS=0.0.0.0
BACKEND_APP_PORT=5001
GUNICORN_TIMEOUT=360

# Production Logging Configuration
LOG_LEVEL=INFO
LOG_FOLDER=logs
LOG_FILE_MAX_SIZE=100
LOG_FILE_BACKUP_COUNT=10
LOG_TZ=UTC

# CRITICAL: Security Headers Configuration for Production
SECURITY_HEADERS_ENABLED=true

# HSTS Configuration (HTTPS enforcement)
SECURITY_HSTS_ENABLED=true
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_HSTS_INCLUDE_SUBDOMAINS=true
SECURITY_HSTS_PRELOAD=true

# Content Security Policy (STRICT for production)
SECURITY_CSP_ENABLED=true
SECURITY_CSP_DEFAULT_SRC='self'
SECURITY_CSP_SCRIPT_SRC='self'
SECURITY_CSP_STYLE_SRC='self'
SECURITY_CSP_IMG_SRC='self' data: https:
SECURITY_CSP_FONT_SRC='self'
SECURITY_CSP_CONNECT_SRC='self'
SECURITY_CSP_FRAME_ANCESTORS='none'

# Frame Protection
SECURITY_X_FRAME_OPTIONS=DENY
SECURITY_X_CONTENT_TYPE_OPTIONS=true
SECURITY_X_XSS_PROTECTION=true

# Referrer and Permissions Policy
SECURITY_REFERRER_POLICY=strict-origin-when-cross-origin
SECURITY_PERMISSIONS_POLICY_ENABLED=true
SECURITY_PERMISSIONS_POLICY=geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()

# Server Header Control (choose one approach)
SECURITY_HIDE_SERVER_HEADER=true
# SECURITY_SERVER_HEADER_VALUE=Madcrow-API  # Only used when HIDE=false

# IMPORTANT: Restrict CORS to your actual domains
WEB_API_CORS_ALLOW_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Performance Settings
API_COMPRESSION_ENABLED=true

# Database Configuration (Production)
DB_USERNAME=madcrow_prod_user
DB_PASSWORD=your-strong-database-password
DB_HOST=your-production-db-host
DB_PORT=5432
DB_DATABASE=madcrow_production
DB_CHARSET=utf8
DB_EXTRAS=sslmode=require&connect_timeout=10

# Database Connection Settings
SQLALCHEMY_DATABASE_URI_SCHEME=postgresql+psycopg
SQLALCHEMY_ECHO=false
SQLALCHEMY_POOL_SIZE=30
SQLALCHEMY_MAX_OVERFLOW=10
SQLALCHEMY_POOL_RECYCLE=3600
SQLALCHEMY_POOL_PRE_PING=true
DB_CONNECTION_TEST_ON_STARTUP=true

# Redis Configuration (Production)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=your-redis-password
REDIS_USE_SSL=true
REDIS_DB=0

# Additional Production Settings
RESPECT_XFORWARD_HEADERS_ENABLED=true

# SSRF Protection
SSRF_PROXY_ALL_URL=
SSRF_PROXY_HTTP_URL=
SSRF_PROXY_HTTPS_URL=
SSRF_DEFAULT_MAX_RETRIES=3
SSRF_DEFAULT_TIME_OUT=5
SSRF_DEFAULT_CONNECT_TIME_OUT=5
SSRF_DEFAULT_READ_TIME_OUT=5
SSRF_DEFAULT_WRITE_TIME_OUT=5

# HTTP Request Limits
HTTP_REQUEST_MAX_CONNECT_TIMEOUT=10
HTTP_REQUEST_MAX_READ_TIMEOUT=60
HTTP_REQUEST_MAX_WRITE_TIMEOUT=20
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576

# PRODUCTION DEPLOYMENT CHECKLIST:
#
# 1. SECURITY (CRITICAL):
#    ✅ Set DEBUG=false
#    ✅ Set DEPLOY_ENV=PRODUCTION
#    ✅ Generate strong SECRET_KEY (openssl rand -base64 42)
#    ✅ Remove 'unsafe-inline' from CSP policies
#    ✅ Restrict CORS origins to your domains
#    ✅ Enable HSTS with preload
#    ✅ Use strong database passwords
#    ✅ Enable database SSL (sslmode=require)
#
# 2. PERFORMANCE:
#    ✅ Enable API compression
#    ✅ Set appropriate connection pool sizes
#    ✅ Configure Redis for caching
#
# 3. MONITORING:
#    ✅ Set LOG_LEVEL=INFO (not DEBUG)
#    ✅ Configure log rotation
#    ✅ Enable health checks
#
# 4. VALIDATION:
#    ✅ Run: uv run python scripts/production_audit.py
#    ✅ Test: uv run python test_security_headers.py
#    ✅ Verify: https://securityheaders.com/
#
# 5. INFRASTRUCTURE:
#    ✅ HTTPS/SSL certificate configured
#    ✅ Load balancer health checks
#    ✅ Database backups configured
#    ✅ Monitoring and alerting setup
