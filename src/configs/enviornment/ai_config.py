"""AI configuration settings for OpenAI and LangChain integration."""

from pydantic import Field
from pydantic_settings import BaseSettings


class AIConfig(BaseSettings):
    """
    Configuration settings for AI services (OpenAI, LangChain)
    """

    # OpenAI Configuration
    OPENAI_API_KEY: str | None = Field(
        description="OpenAI API key for accessing GPT models",
        default=None,
    )

    OPENAI_MODEL: str = Field(
        description="Default OpenAI model to use for chat",
        default="gpt-3.5-turbo",
    )

    OPENAI_MAX_TOKENS: int = Field(
        description="Maximum tokens for OpenAI responses",
        default=1000,
    )

    OPENAI_TEMPERATURE: float = Field(
        description="Temperature setting for OpenAI responses (0.0 to 2.0)",
        default=0.7,
        ge=0.0,
        le=2.0,
    )

    # LangChain Configuration
    LANGCHAIN_TRACING_V2: bool = Field(
        description="Enable LangChain tracing",
        default=False,
    )

    LANGCHAIN_ENDPOINT: str | None = Field(
        description="LangChain endpoint for tracing",
        default=None,
    )

    LANGCHAIN_API_KEY: str | None = Field(
        description="LangChain API key for tracing",
        default=None,
    )

    LANGCHAIN_PROJECT: str = Field(
        description="LangChain project name",
        default="madcrow-chat",
    )

    # Chat Configuration
    CHAT_MAX_HISTORY: int = Field(
        description="Maximum number of messages to include in chat history",
        default=50,
    )

    CHAT_SYSTEM_PROMPT: str = Field(
        description="Default system prompt for chat assistant",
        default="You are a helpful AI assistant. Provide clear, accurate, and helpful responses. Be concise but thorough in your explanations.",
    )

    # Rate Limiting for AI
    AI_RATE_LIMIT_ENABLED: bool = Field(
        description="Enable rate limiting for AI requests",
        default=True,
    )

    AI_RATE_LIMIT_MAX_REQUESTS: int = Field(
        description="Maximum AI requests per time window",
        default=100,
    )

    AI_RATE_LIMIT_TIME_WINDOW: int = Field(
        description="Time window for AI rate limiting in seconds",
        default=3600,  # 1 hour
    ) 