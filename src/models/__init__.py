"""Models package for FastAPI request/response validation."""

from .auth import (
    LoginRequest,
    LoginResponse,
    LogoutRequest,
    LogoutResponse,
    PasswordChangeRequest,
    PasswordChangeResponse,
    SessionInfo,
    SessionValidationResponse,
    UserProfile,
)
from .chat import (
    ChatBase,
    ChatCreate,
    ChatDeleteResponse,
    ChatHistoryResponse,
    ChatListResponse,
    ChatMessageRequest,
    ChatMessageResponse,
    ChatResponse,
    ChatUpdate,
    MessageBase,
    MessageCreate,
    MessageResponse,
    StreamingChatResponse,
)
from .errors import (
    AuthenticationErrorResponse,
    AuthorizationErrorResponse,
    BaseErrorResponse,
    BusinessErrorResponse,
    DatabaseErrorResponse,
    ErrorContext,
    ErrorDetail,
    InternalServerErrorResponse,
    ValidationErrorResponse,
)
from .health import HealthResponse
from .profile import (
    ProfilePreferencesRequest,
    ProfilePreferencesResponse,
    ProfileStatsResponse,
    ProfileUpdateRequest,
    ProfileUpdateResponse,
)
from .rate_limit import (
    RateLimitExceededResponse,
    RateLimitHeaders,
    RateLimitInfo,
)
from .token import TokenClaims

__all__ = [
    "AuthenticationErrorResponse",
    "AuthorizationErrorResponse",
    "BaseErrorResponse",
    "BusinessErrorResponse",
    "ChatBase",
    "ChatCreate",
    "ChatDeleteResponse",
    "ChatHistoryResponse",
    "ChatListResponse",
    "ChatMessageRequest",
    "ChatMessageResponse",
    "ChatResponse",
    "ChatUpdate",
    "DatabaseErrorResponse",
    "ErrorContext",
    "ErrorDetail",
    "HealthResponse",
    "InternalServerErrorResponse",
    "LoginRequest",
    "LoginResponse",
    "LogoutRequest",
    "LogoutResponse",
    "MessageBase",
    "MessageCreate",
    "MessageResponse",
    "PasswordChangeRequest",
    "PasswordChangeResponse",
    "ProfilePreferencesRequest",
    "ProfilePreferencesResponse",
    "ProfileStatsResponse",
    "ProfileUpdateRequest",
    "ProfileUpdateResponse",
    "RateLimitExceededResponse",
    "RateLimitHeaders",
    "RateLimitInfo",
    "SessionInfo",
    "SessionValidationResponse",
    "StreamingChatResponse",
    "TokenClaims",
    "UserProfile",
    "ValidationErrorResponse",
]
