{"files.exclude": {"**/__pycache__": true, "**/*.egg-info": true, "**/*.egg": true}, "python.analysis.extraPaths": ["./src"], "python.defaultInterpreterPath": "./.venv/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.lintOnSave": true, "python.linting.ruffEnabled": true, "python.linting.flake8Enabled": false, "python.linting.pylintEnabled": false, "python.linting.mypyEnabled": false, "python.linting.ruffArgs": ["--config=pyproject.toml"], "python.formatting.provider": "ruff", "python.formatting.ruffArgs": ["--config=pyproject.toml"], "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}}