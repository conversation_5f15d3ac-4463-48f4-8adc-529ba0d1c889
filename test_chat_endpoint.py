#!/usr/bin/env python3
"""Test script for chat endpoints."""

import json
import requests
from uuid import uuid4

# Configuration
BASE_URL = "http://localhost:5001/api/v1"
CHAT_BASE_URL = "http://localhost:5001/api/v1/chat"

# Test credentials
EMAIL = "<EMAIL>"
PASSWORD = "password@123S"

def test_login():
    """Test login and get access token."""
    print("🔐 Testing login...")
    
    login_data = {
        "email": EMAIL,
        "password": PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        print(f"Login status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful!")
            print(f"Response structure: {list(data.keys())}")
            
            # Extract access token
            if "data" in data and "access_token" in data["data"]:
                access_token = data["data"]["access_token"]
                print(f"🎫 Access token obtained (length: {len(access_token)})")
                return access_token
            else:
                print(f"❌ Unexpected response structure: {data}")
                return None
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_chat_test_endpoint():
    """Test the /chat/test endpoint (no auth required)."""
    print("\n💬 Testing chat test endpoint...")
    
    test_message = {
        "message": "Hello, this is a test message!",
        "stream": False
    }
    
    try:
        response = requests.post(f"{CHAT_BASE_URL}/test", json=test_message)
        print(f"Chat test status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat test successful!")
            print(f"Response: {json.dumps(data, indent=2, default=str)}")
        else:
            print(f"❌ Chat test failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Chat test error: {e}")

def test_chat_endpoint_with_auth(access_token):
    """Test the main chat endpoint with authentication."""
    print("\n💬 Testing authenticated chat endpoint...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    chat_message = {
        "message": "Hello AI! Can you help me with a simple question?",
        "stream": False
    }
    
    try:
        response = requests.post(f"{CHAT_BASE_URL}/", json=chat_message, headers=headers)
        print(f"Chat status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat successful!")
            print(f"Response: {json.dumps(data, indent=2, default=str)}")
            return data.get("chat_id")
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return None

def test_get_chats(access_token):
    """Test getting user chats."""
    print("\n📋 Testing get chats endpoint...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{CHAT_BASE_URL}/", headers=headers)
        print(f"Get chats status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Get chats successful!")
            print(f"Response: {json.dumps(data, indent=2, default=str)}")
        else:
            print(f"❌ Get chats failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Get chats error: {e}")

def test_chat_history(access_token, chat_id):
    """Test getting chat history."""
    if not chat_id:
        print("\n⏭️  Skipping chat history test (no chat_id)")
        return
        
    print(f"\n📜 Testing chat history endpoint for chat {chat_id}...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{CHAT_BASE_URL}/{chat_id}/history", headers=headers)
        print(f"Chat history status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat history successful!")
            print(f"Response: {json.dumps(data, indent=2, default=str)}")
        else:
            print(f"❌ Chat history failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Chat history error: {e}")

def main():
    """Main test function."""
    print("🚀 Starting chat endpoint tests...")
    print(f"Base URL: {BASE_URL}")
    print(f"Chat URL: {CHAT_BASE_URL}")
    print(f"Credentials: {EMAIL} / {PASSWORD}")
    
    # Test 1: Login to get access token
    access_token = test_login()
    
    # Test 2: Test the no-auth test endpoint
    test_chat_test_endpoint()
    
    if access_token:
        # Test 3: Test authenticated chat endpoint
        chat_id = test_chat_endpoint_with_auth(access_token)
        
        # Test 4: Get user chats
        test_get_chats(access_token)
        
        # Test 5: Get chat history
        test_chat_history(access_token, chat_id)
    else:
        print("\n⚠️  Skipping authenticated tests due to login failure")
    
    print("\n🏁 Chat endpoint tests completed!")

if __name__ == "__main__":
    main()
