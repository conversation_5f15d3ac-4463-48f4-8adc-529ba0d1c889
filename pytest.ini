[tool:pytest]
# Pytest configuration for FastAPI Madcrow project

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Ignore patterns
norecursedirs =
    docker
    .git
    .venv
    __pycache__
    *.egg-info
    .pytest_cache
    htmlcov

# Minimum version
minversion = 6.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --color=yes
    --durations=10
    --showlocals
    --disable-warnings

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    api: API endpoint tests
    e2e: End-to-end workflow tests
    performance: Performance and load tests
    security: Security-focused tests
    edge_cases: Edge case and boundary condition tests
    slow: Tests that take longer to run
    auth: Authentication-related tests
    database: Database operation tests
    redis: Redis operation tests
    rate_limit: Rate limiting tests
    smoke: Quick smoke tests for basic functionality

# Test filtering
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Coverage settings are in .coveragerc file
