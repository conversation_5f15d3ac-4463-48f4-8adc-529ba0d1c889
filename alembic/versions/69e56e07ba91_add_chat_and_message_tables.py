"""add_chat_and_message_tables

Revision ID: 69e56e07ba91
Revises: 3c35a1d92ec6
Create Date: 2025-07-28 14:10:57.791404

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '69e56e07ba91'
down_revision: Union[str, Sequence[str], None] = '3c35a1d92ec6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
