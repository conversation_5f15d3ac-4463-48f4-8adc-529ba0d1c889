repos:
  # Basic file and syntax checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
        exclude: core/model_runtime/model_providers/__base/tokenizers/gpt2/vocab.json
      - id: check-ast
      - id: check-case-conflict
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-executables-have-shebangs
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: fix-byte-order-marker
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.3.1
    hooks:
      - id: autoflake
        args: [--remove-all-unused-imports, --in-place]
        stages: [pre-commit]
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
      - id: pyupgrade
        args: [--py310-plus]
        stages: [pre-commit]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.2
    hooks:
      - id: ruff
        args: [--fix]
        stages: [pre-commit]
      - id: ruff-format
        stages: [pre-commit]
  # Security vulnerability scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.6
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: [".[toml]"]
        stages: [pre-push]

  # Type checking with mypy (lenient configuration for gradual adoption)
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.1
    hooks:
      - id: mypy
        additional_dependencies: [pydantic, fastapi, sqlmodel, types-requests]
        args: ["--config-file=mypy.ini"]
        files: ^src/
        exclude: ^(tests/|migrations/|alembic/)
        stages: [manual] # Run manually with: pre-commit run mypy --hook-stage manual

  # Dependency vulnerability auditing (safety disabled due to compatibility issues)
  # - repo: local
  #   hooks:
  #     - id: safety-check
  #       name: safety-check
  #       entry: uv run safety check --ignore=70612
  #       language: system
  #       types: [python]
  #       stages: [pre-push]
  #       pass_filenames: false

  # Check for secrets and sensitive information
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ["--baseline", ".secrets.baseline"]
        exclude: ^(\.env\.example|\.env\.production\.example|alembic/)
        stages: [pre-commit]

  # Python import sorting
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]
        stages: [pre-commit]

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.13.1-beta
    hooks:
      - id: hadolint-docker
        stages: [pre-commit]

  # YAML formatting and validation
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        types_or: [yaml, markdown, json]
        exclude: ^(\.vscode/|\.github/)
        stages: [pre-commit]

  # Check for common Python anti-patterns
  - repo: https://github.com/PyCQA/flake8
    rev: 7.3.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
        args: ["--max-line-length=100", "--extend-ignore=E203,E501,B008"]
        files: ^src/
        exclude: ^(migrations/|tests/|alembic/)
        stages: [pre-push]

  # Check requirements.txt for known vulnerabilities
  - repo: local
    hooks:
      - id: pip-audit
        name: pip-audit
        entry: uv run pip-audit
        language: system
        types: [python]
        stages: [pre-push]
        pass_filenames: false
