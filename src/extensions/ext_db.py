import logging
from typing import Any

from sqlalchemy import text
from sqlalchemy.engine import Engine
from sqlmodel import Session, create_engine

from ..beco_app import <PERSON><PERSON><PERSON><PERSON>
from ..configs import madcrow_config

logger = logging.getLogger(__name__)


class DBEngine:
    """Database engine manager with proper lifecycle and error handling."""

    def __init__(self):
        self._engine: Engine | None = None
        self._is_initialized = False

    def init_app(self, app: BecoApp) -> None:
        """Initialize database engine and store it on the app."""
        if self._is_initialized:
            logger.warning("Database engine already initialized")
            return

        try:
            logger.info("Initializing database engine...")
            logger.info(f"Connecting to database: {madcrow_config.DB_HOST}:{madcrow_config.DB_PORT}/{madcrow_config.DB_DATABASE}")

            # Create database URL
            database_url = (
                f"{madcrow_config.SQLALCHEMY_DATABASE_URI_SCHEME}://"
                f"{madcrow_config.DB_USERNAME}:{madcrow_config.DB_PASSWORD}@"
                f"{madcrow_config.DB_HOST}:{madcrow_config.DB_PORT}/"
                f"{madcrow_config.DB_DATABASE}"
            )

            # Create engine with configuration
            self._engine = create_engine(
                database_url,
                echo=madcrow_config.SQLALCHEMY_ECHO,
                pool_size=madcrow_config.SQLALCHEMY_POOL_SIZE,
                max_overflow=madcrow_config.SQLALCHEMY_MAX_OVERFLOW,
                pool_recycle=madcrow_config.SQLALCHEMY_POOL_RECYCLE,
                pool_pre_ping=madcrow_config.SQLALCHEMY_POOL_PRE_PING,
            )

            # Test the connection if enabled
            if madcrow_config.DB_CONNECTION_TEST_ON_STARTUP:
                connection_test_passed = self._test_connection()

                if not connection_test_passed:
                    if madcrow_config.DEPLOY_ENV == "PRODUCTION":
                        raise RuntimeError("Database connection test failed in production environment")
                    else:
                        logger.warning("Database connection test failed, but continuing in development mode")
            else:
                logger.info("Database connection test skipped (disabled in configuration)")

            # Store engine on app for global access
            app.state.engine = self._engine
            self._is_initialized = True

            logger.info("Database engine initialized successfully")

        except Exception as e:
            logger.exception("Failed to initialize database engine")
            if madcrow_config.DEPLOY_ENV == "PRODUCTION":
                raise RuntimeError(f"Database initialization failed: {e}") from e
            else:
                logger.warning(f"Database initialization failed in development mode: {e}")
                # In development mode, create a dummy engine to allow the app to start
                logger.info("Creating dummy database engine for development mode")
                self._engine = None
                self._is_initialized = True
                app.state.engine = None

    def _test_connection(self) -> bool:
        """Test database connection during initialization."""
        if not self._engine:
            logger.error("Cannot test connection: Engine not initialized")
            return False

        try:
            logger.debug("Testing database connection...")
            with Session(self._engine) as session:
                # Simple connection test
                result = session.execute(text("SELECT 1")).scalar()
                logger.debug(f"Connection test result: {result}, type: {type(result)}")

                # Handle different result types more robustly
                if result is None:
                    logger.error("Database connection test failed: No result returned")
                    return False

                # Try to convert to int for comparison
                try:
                    result_value = int(result)
                    success = result_value == 1
                except (ValueError, TypeError):
                    # If result is a tuple/row, try to get first element
                    try:
                        if hasattr(result, "__getitem__"):
                            result_value = int(result[0])
                            success = result_value == 1
                        else:
                            # Just check if we got any result
                            success = True
                            logger.debug(f"Got non-integer result {result}, considering successful")
                    except Exception:
                        success = True
                        logger.debug(f"Got result {result}, considering successful")

                if success:
                    logger.info("Database connection test passed")
                else:
                    logger.error(f"Database connection test failed: unexpected result {result}")

                return success
        except Exception:
            logger.exception("Database connection test failed")
            logger.debug(
                f"Connection details: {madcrow_config.DB_HOST}:{madcrow_config.DB_PORT}/{madcrow_config.DB_DATABASE}"
            )
            return False

    def get_engine(self) -> Engine:
        """Get the database engine with validation."""
        if not self._is_initialized or not self._engine:
            raise RuntimeError("Database engine not initialized. Call init_app() first.")
        return self._engine

    def is_healthy(self) -> bool:
        """Check if database connection is healthy."""
        if not self._is_initialized or not self._engine:
            logger.debug("Database health check: engine not initialized")
            return False

        try:
            with Session(self._engine) as session:
                result = session.execute(text("SELECT 1")).scalar()
                logger.debug(f"Health check result: {result}, type: {type(result)}")

                # Handle different result types
                if result is None:
                    logger.warning("Database health check: No result returned")
                    return False

                # Try to convert to int for comparison
                try:
                    result_value = int(result)
                    healthy = result_value == 1
                except (ValueError, TypeError):
                    # If result is a tuple/row, try to get first element
                    try:
                        if hasattr(result, "__getitem__"):
                            result_value = int(result[0])
                            healthy = result_value == 1
                        else:
                            # Just check if we got any result
                            healthy = True
                            logger.debug(f"Health check: Got result {result}, considering healthy")
                    except Exception:
                        healthy = True
                        logger.debug(f"Health check: Got result {result}, considering healthy")

                if healthy:
                    logger.debug("Database health check: passed")
                else:
                    logger.warning(f"Database health check: failed - unexpected result {result}")
                return healthy
        except Exception as e:
            logger.warning(f"Database health check failed: {e}")
            return False

    def close(self) -> None:
        """Close database engine and cleanup resources."""
        if self._engine:
            logger.info("Closing database engine...")
            self._engine.dispose()
            self._engine = None
            self._is_initialized = False
            logger.info("Database engine closed")


db_engine = DBEngine()


def is_enabled() -> bool:
    """Check if database extension should be enabled."""
    return True  # Database is always required


def init_app(app: BecoApp) -> None:
    """Initialize database extension with proper error handling."""
    try:
        db_engine.init_app(app)
        logger.info("Database extension initialized successfully")
    except Exception:
        logger.exception("Failed to initialize database extension")
        raise


def cleanup() -> None:
    """Cleanup database resources. Called by lifespan manager."""
    db_engine.close()
