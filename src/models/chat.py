"""Chat API models for request/response validation."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ChatBase(BaseModel):
    """Base chat model."""
    title: str = Field(..., min_length=1, max_length=255, description="Chat title")


class ChatCreate(ChatBase):
    """Model for creating a new chat."""
    pass


class ChatUpdate(BaseModel):
    """Model for updating chat title."""
    title: str = Field(..., min_length=1, max_length=255, description="New chat title")


class ChatResponse(ChatBase):
    """Model for chat response."""
    id: UUID
    user_id: UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime
    message_count: Optional[int] = Field(None, description="Number of messages in the chat")

    class Config:
        from_attributes = True


class MessageBase(BaseModel):
    """Base message model."""
    content: str = Field(..., min_length=1, description="Message content")


class MessageCreate(MessageBase):
    """Model for creating a new message."""
    role: str = Field(..., description="Message role (user or assistant)")


class MessageResponse(MessageBase):
    """Model for message response."""
    id: UUID
    chat_id: UUID
    role: str
    tokens_used: Optional[str] = Field(None, description="Number of tokens used")
    llm_model: Optional[str] = Field(None, description="Model used for generation")
    created_at: datetime

    class Config:
        from_attributes = True


class ChatMessageRequest(BaseModel):
    """Model for chat message request."""
    message: str = Field(..., min_length=1, description="User message")
    chat_id: Optional[UUID] = Field(None, description="Chat ID (optional, creates new chat if not provided)")
    stream: bool = Field(False, description="Whether to stream the response")


class ChatMessageResponse(BaseModel):
    """Model for chat message response."""
    chat_id: UUID
    message_id: UUID
    response: str
    tokens_used: Optional[str] = None
    llm_model: Optional[str] = None


class ChatHistoryResponse(BaseModel):
    """Model for chat history response."""
    chat: ChatResponse
    messages: List[MessageResponse]
    total_messages: int


class ChatListResponse(BaseModel):
    """Model for chat list response."""
    chats: List[ChatResponse]
    total: int
    limit: int
    offset: int


class ChatDeleteResponse(BaseModel):
    """Model for chat deletion response."""
    success: bool
    message: str = "Chat deleted successfully"


class StreamingChatResponse(BaseModel):
    """Model for streaming chat response."""
    chat_id: UUID
    message_id: Optional[UUID] = None
    content: str
    is_complete: bool = False
    tokens_used: Optional[str] = None
    llm_model: Optional[str] = None 