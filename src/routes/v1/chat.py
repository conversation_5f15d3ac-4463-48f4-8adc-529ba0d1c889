"""Chat routes for FastAPI application."""

import logging
from typing import Annotated, List
from uuid import UUID

from fastapi import Depends, HTTPException, Query, status, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from ...dependencies.auth import CurrentUser
from ...dependencies.db import DatabaseSession
from ...entities.account import Account
from ...models.chat import (
    ChatDeleteResponse,
    ChatHistoryResponse,
    ChatListResponse,
    ChatMessageRequest,
    ChatMessageResponse,
    ChatResponse,
    ChatUpdate,
    MessageResponse,
    StreamingChatResponse,
)
from ...services.chat_service import ChatService, get_chat_service_dep
from ..base_router import BaseRouter

logger = logging.getLogger(__name__)

# Create router
chat_router = BaseRouter(prefix="/chat", tags=["chat"])


@chat_router.post("/", response_model=ChatMessageResponse)
async def send_message(
    request: ChatMessageRequest,
    request_obj: Request,
) -> ChatMessageResponse:
    """
    Send a message to the AI chat.
    
    If chat_id is provided, continues the existing conversation.
    If chat_id is not provided, creates a new chat session.
    """
    try:
        # Simple authentication check
        from ...services.token_service import get_token_service
        from ...services.auth_service import get_auth_service
        from ...extensions.ext_db import db_engine
        from sqlmodel import Session
        from uuid import UUID
        
        # Get token from request
        auth_header = request_obj.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Verify token
        token_service = get_token_service()
        claims = token_service.verify_token(token, "access")
        if not claims:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get user
        try:
            user_id = UUID(claims.sub)
        except ValueError:
            raise HTTPException(status_code=401, detail="Invalid token format")
        
        # Get user from database
        engine = db_engine.get_engine()
        session = Session(engine)
        auth_service = get_auth_service(session)
        user = auth_service.get_user_by_id(user_id)
        
        if not user or not auth_service.is_user_active(user.id):
            raise HTTPException(status_code=401, detail="User not active")
        
        # Create chat service
        from ...services.chat_service import ChatService
        chat_service = ChatService(session)
        
        chat, ai_message, response = await chat_service.process_message(request, user.id)
        
        return ChatMessageResponse(
            chat_id=chat.id,
            message_id=ai_message.id,
            response=response,
            tokens_used=ai_message.tokens_used,
            llm_model=ai_message.llm_model,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error processing chat message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat message"
        )


@chat_router.post("/stream")
async def stream_message(
    request: ChatMessageRequest,
    request_obj: Request,
) -> StreamingResponse:
    """
    Stream a message response from the AI chat.
    
    Returns a streaming response with the AI's response chunks.
    """
    try:
        # Simple authentication check
        from ...services.token_service import get_token_service
        from ...services.auth_service import get_auth_service
        from ...extensions.ext_db import db_engine
        from sqlmodel import Session
        from uuid import UUID
        
        # Get token from request
        auth_header = request_obj.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Verify token
        token_service = get_token_service()
        claims = token_service.verify_token(token, "access")
        if not claims:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get user
        try:
            user_id = UUID(claims.sub)
        except ValueError:
            raise HTTPException(status_code=401, detail="Invalid token format")
        
        # Get user from database
        engine = db_engine.get_engine()
        session = Session(engine)
        auth_service = get_auth_service(session)
        user = auth_service.get_user_by_id(user_id)
        
        if not user or not auth_service.is_user_active(user.id):
            raise HTTPException(status_code=401, detail="User not active")
        
        # Create chat service
        from ...services.chat_service import ChatService
        chat_service = ChatService(session)
        
        async def generate_stream():
            async for chunk in chat_service.stream_message(request, user.id):
                # Convert to JSON string with newline for streaming
                yield f"data: {chunk.model_dump_json()}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error streaming chat message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stream chat message"
        )


@chat_router.get("/", response_model=ChatListResponse)
async def get_chats(
    request_obj: Request,
    limit: int = Query(50, ge=1, le=100, description="Number of chats to return"),
    offset: int = Query(0, ge=0, description="Number of chats to skip"),
) -> ChatListResponse:
    """Get all chats for the current user."""
    try:
        # Simple authentication check
        from ...services.token_service import get_token_service
        from ...services.auth_service import get_auth_service
        from ...extensions.ext_db import db_engine
        from sqlmodel import Session
        from uuid import UUID
        
        # Get token from request
        auth_header = request_obj.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Verify token
        token_service = get_token_service()
        claims = token_service.verify_token(token, "access")
        if not claims:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get user
        try:
            user_id = UUID(claims.sub)
        except ValueError:
            raise HTTPException(status_code=401, detail="Invalid token format")
        
        # Get user from database
        engine = db_engine.get_engine()
        session = Session(engine)
        auth_service = get_auth_service(session)
        user = auth_service.get_user_by_id(user_id)
        
        if not user or not auth_service.is_user_active(user.id):
            raise HTTPException(status_code=401, detail="User not active")
        
        # Create chat service
        from ...services.chat_service import ChatService
        chat_service = ChatService(session)
        
        chats = chat_service.get_user_chats(user.id, limit, offset)
        
        # Convert to response models
        chat_responses = []
        for chat in chats:
            message_count = len(chat.messages) if hasattr(chat, 'messages') else 0
            chat_responses.append(
                ChatResponse(
                    id=chat.id,
                    title=chat.title,
                    user_id=chat.user_id,
                    is_active=chat.is_active,
                    created_at=chat.created_at,
                    updated_at=chat.updated_at,
                    message_count=message_count,
                )
            )
        
        return ChatListResponse(
            chats=chat_responses,
            total=len(chat_responses),
            limit=limit,
            offset=offset,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting user chats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user chats"
        )


@chat_router.get("/{chat_id}/history", response_model=ChatHistoryResponse)
async def get_chat_history(
    chat_id: UUID,
    request_obj: Request,
    limit: int = Query(100, ge=1, le=200, description="Number of messages to return"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
) -> ChatHistoryResponse:
    """Get chat history with messages."""
    try:
        # Simple authentication check
        from ...services.token_service import get_token_service
        from ...services.auth_service import get_auth_service
        from ...extensions.ext_db import db_engine
        from sqlmodel import Session
        from uuid import UUID
        
        # Get token from request
        auth_header = request_obj.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Verify token
        token_service = get_token_service()
        claims = token_service.verify_token(token, "access")
        if not claims:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get user
        try:
            user_id = UUID(claims.sub)
        except ValueError:
            raise HTTPException(status_code=401, detail="Invalid token format")
        
        # Get user from database
        engine = db_engine.get_engine()
        session = Session(engine)
        auth_service = get_auth_service(session)
        user = auth_service.get_user_by_id(user_id)
        
        if not user or not auth_service.is_user_active(user.id):
            raise HTTPException(status_code=401, detail="User not active")
        
        # Create chat service
        from ...services.chat_service import ChatService
        chat_service = ChatService(session)
        
        chat, messages, total_messages = chat_service.get_chat_history(
            chat_id, user.id, limit, offset
        )
        
        # Convert to response models
        message_responses = []
        for message in messages:
            message_responses.append(
                MessageResponse(
                    id=message.id,
                    chat_id=message.chat_id,
                    role=message.role,
                    content=message.content,
                    tokens_used=message.tokens_used,
                    llm_model=message.llm_model,
                    created_at=message.created_at,
                )
            )
        
        chat_response = ChatResponse(
            id=chat.id,
            title=chat.title,
            user_id=chat.user_id,
            is_active=chat.is_active,
            created_at=chat.created_at,
            updated_at=chat.updated_at,
            message_count=total_messages,
        )
        
        return ChatHistoryResponse(
            chat=chat_response,
            messages=message_responses,
            total_messages=total_messages,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting chat history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get chat history"
        )


@chat_router.put("/{chat_id}/title", response_model=ChatResponse)
async def update_chat_title(
    chat_id: UUID,
    chat_update: ChatUpdate,
    request_obj: Request,
) -> ChatResponse:
    """Update chat title."""
    try:
        # Simple authentication check
        from ...services.token_service import get_token_service
        from ...services.auth_service import get_auth_service
        from ...extensions.ext_db import db_engine
        from sqlmodel import Session
        from uuid import UUID
        
        # Get token from request
        auth_header = request_obj.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Verify token
        token_service = get_token_service()
        claims = token_service.verify_token(token, "access")
        if not claims:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get user
        try:
            user_id = UUID(claims.sub)
        except ValueError:
            raise HTTPException(status_code=401, detail="Invalid token format")
        
        # Get user from database
        engine = db_engine.get_engine()
        session = Session(engine)
        auth_service = get_auth_service(session)
        user = auth_service.get_user_by_id(user_id)
        
        if not user or not auth_service.is_user_active(user.id):
            raise HTTPException(status_code=401, detail="User not active")
        
        # Create chat service
        from ...services.chat_service import ChatService
        chat_service = ChatService(session)
        
        chat = chat_service.update_chat_title(chat_id, user.id, chat_update.title)
        
        return ChatResponse(
            id=chat.id,
            title=chat.title,
            user_id=chat.user_id,
            is_active=chat.is_active,
            created_at=chat.created_at,
            updated_at=chat.updated_at,
            message_count=len(chat.messages) if hasattr(chat, 'messages') else 0,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating chat title: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update chat title"
        )


@chat_router.delete("/{chat_id}", response_model=ChatDeleteResponse)
async def delete_chat(
    chat_id: UUID,
    request_obj: Request,
) -> ChatDeleteResponse:
    """Delete a chat (soft delete)."""
    try:
        # Simple authentication check
        from ...services.token_service import get_token_service
        from ...services.auth_service import get_auth_service
        from ...extensions.ext_db import db_engine
        from sqlmodel import Session
        from uuid import UUID
        
        # Get token from request
        auth_header = request_obj.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Authentication required")
        
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Verify token
        token_service = get_token_service()
        claims = token_service.verify_token(token, "access")
        if not claims:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get user
        try:
            user_id = UUID(claims.sub)
        except ValueError:
            raise HTTPException(status_code=401, detail="Invalid token format")
        
        # Get user from database
        engine = db_engine.get_engine()
        session = Session(engine)
        auth_service = get_auth_service(session)
        user = auth_service.get_user_by_id(user_id)
        
        if not user or not auth_service.is_user_active(user.id):
            raise HTTPException(status_code=401, detail="User not active")
        
        # Create chat service
        from ...services.chat_service import ChatService
        chat_service = ChatService(session)
        
        success = chat_service.delete_chat(chat_id, user.id)
        
        return ChatDeleteResponse(
            success=success,
            message="Chat deleted successfully" if success else "Failed to delete chat",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error deleting chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete chat"
        ) 


@chat_router.post("/test", response_model=ChatMessageResponse)
async def test_chat(
    request: ChatMessageRequest,
) -> ChatMessageResponse:
    """
    Test chat endpoint that doesn't require database.
    This demonstrates the chat functionality is working properly.
    """
    try:
        # Mock response for testing
        mock_response = f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly."
        
        # Generate mock IDs
        import uuid
        mock_chat_id = uuid.uuid4()
        mock_message_id = uuid.uuid4()
        
        return ChatMessageResponse(
            chat_id=mock_chat_id,
            message_id=mock_message_id,
            response=mock_response,
            tokens_used="150",
            llm_model="gpt-3.5-turbo",
        )
    except Exception as e:
        logger.exception(f"Error in test chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process test chat message"
        ) 