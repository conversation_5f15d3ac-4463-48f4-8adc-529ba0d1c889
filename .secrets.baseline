{"version": "1.5.0", "plugins_used": [{"name": "ArtifactoryDetector"}, {"name": "AWSKeyDetector"}, {"name": "AzureStorageKeyDetector"}, {"name": "Base64HighEntropyString", "limit": 4.5}, {"name": "BasicAuthDetector"}, {"name": "CloudantDetector"}, {"name": "DiscordBotTokenDetector"}, {"name": "GitHubTokenDetector"}, {"name": "GitLabTokenDetector"}, {"name": "HexHighEntropyString", "limit": 3.0}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "IPPublicDetector"}, {"name": "JwtTokenDetector"}, {"name": "KeywordDetector", "keyword_exclude": ""}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "OpenAIDetector"}, {"name": "PrivateKeyDetector"}, {"name": "PypiTokenDetector"}, {"name": "SendGridDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TelegramBotTokenDetector"}, {"name": "TwilioKeyDetector"}], "filters_used": [{"path": "detect_secrets.filters.allowlist.is_line_allowlisted"}, {"path": "detect_secrets.filters.common.is_baseline_file", "filename": ".secrets.baseline"}, {"path": "detect_secrets.filters.common.is_ignored_due_to_verification_policies", "min_level": 2}, {"path": "detect_secrets.filters.heuristic.is_indirect_reference"}, {"path": "detect_secrets.filters.heuristic.is_likely_id_string"}, {"path": "detect_secrets.filters.heuristic.is_lock_file"}, {"path": "detect_secrets.filters.heuristic.is_not_alphanumeric_string"}, {"path": "detect_secrets.filters.heuristic.is_potential_uuid"}, {"path": "detect_secrets.filters.heuristic.is_prefixed_with_dollar_sign"}, {"path": "detect_secrets.filters.heuristic.is_sequential_string"}, {"path": "detect_secrets.filters.heuristic.is_swagger_file"}, {"path": "detect_secrets.filters.heuristic.is_templated_secret"}], "results": {"alembic.ini": [{"type": "Basic Auth Credentials", "filename": "alembic.ini", "hashed_secret": "9d4e1e23bd5b727046a9e3b4b7db57bd8d6ee684", "is_verified": false, "line_number": 87}], "alembic/versions/4358ef05cf7c_initial_migration_with_account_model.py": [{"type": "Hex High Entropy String", "filename": "alembic/versions/4358ef05cf7c_initial_migration_with_account_model.py", "hashed_secret": "bcc76aa18afd913d7af0ec5a09466e8dfdc602cd", "is_verified": false, "line_number": 17}], "alembic/versions/6eb75cddafb3_password_salt.py": [{"type": "Hex High Entropy String", "filename": "alembic/versions/6eb75cddafb3_password_salt.py", "hashed_secret": "ade0a9cd6fdd480711180ea7dad5e14f8c8a5ed9", "is_verified": false, "line_number": 17}, {"type": "Hex High Entropy String", "filename": "alembic/versions/6eb75cddafb3_password_salt.py", "hashed_secret": "bcc76aa18afd913d7af0ec5a09466e8dfdc602cd", "is_verified": false, "line_number": 18}], "docs/api-reference.md": [{"type": "Secret Keyword", "filename": "docs/api-reference.md", "hashed_secret": "ecb252044b5ea0f679ee78ec1a12904739e2904d", "is_verified": false, "line_number": 18}, {"type": "Basic Auth Credentials", "filename": "docs/api-reference.md", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 243}, {"type": "Secret Keyword", "filename": "docs/api-reference.md", "hashed_secret": "72559b51f94a7a3ad058c5740cbe2f7cb0d4080b", "is_verified": false, "line_number": 269}], "docs/authentication.md": [{"type": "Secret Keyword", "filename": "docs/authentication.md", "hashed_secret": "463d8353f4b927fd953e5af7c41084183e8ce5fb", "is_verified": false, "line_number": 64}, {"type": "Secret Keyword", "filename": "docs/authentication.md", "hashed_secret": "cbfdac6008f9cab4083784cbd1874f76618d2a97", "is_verified": false, "line_number": 371}, {"type": "Basic Auth Credentials", "filename": "docs/authentication.md", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 423}], "docs/getting-started.md": [{"type": "Basic Auth Credentials", "filename": "docs/getting-started.md", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 34}, {"type": "Secret Keyword", "filename": "docs/getting-started.md", "hashed_secret": "463d8353f4b927fd953e5af7c41084183e8ce5fb", "is_verified": false, "line_number": 106}, {"type": "Secret Keyword", "filename": "docs/getting-started.md", "hashed_secret": "aa6ec3e252f07e4d1854ba06ebbc99f21c458f76", "is_verified": false, "line_number": 132}], "test_auth_login.py": [{"type": "Secret Keyword", "filename": "test_auth_login.py", "hashed_secret": "d8ecf7db8fc9ec9c31bc5c9ae2929cc599c75f8d", "is_verified": false, "line_number": 46}, {"type": "Secret Keyword", "filename": "test_auth_login.py", "hashed_secret": "f865b53623b121fd34ee5426c792e5c33af8c227", "is_verified": false, "line_number": 97}]}, "generated_at": "2025-07-15T06:25:11Z"}