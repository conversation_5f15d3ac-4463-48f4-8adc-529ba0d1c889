"""Chat service for handling LLM interactions with LangChain and OpenAI."""

import logging
import uuid
from datetime import UTC, datetime
from typing import AsyncGenerator, List, Optional
from uuid import UUID

from fastapi import HTTPException, Depends
from langchain_community.chat_models import ChatOpenAI
from langchain.memory import ConversationBufferMemory
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from sqlalchemy.orm import Session
from tiktoken import encoding_for_model

from ..configs import madcrow_config
from ..entities.chat import Chat, Message
from ..models.chat import ChatMessageRequest, StreamingChatResponse
from ..dependencies.db import DatabaseSession

logger = logging.getLogger(__name__)


class ChatService:
    """Service for handling chat operations with LLM integration."""

    def __init__(self, db_session: Session):
        """Initialize chat service."""
        self.db_session = db_session
        self.model_name = "gpt-3.5-turbo"  # Default model
        self.max_tokens = 1000  # Default max tokens
        
        # Initialize OpenAI client
        self._init_openai_client()

    def _init_openai_client(self) -> None:
        """Initialize OpenAI client."""
        try:
            api_key = madcrow_config.OPENAI_API_KEY
            if not api_key:
                logger.warning("OpenAI API key not configured")
                return
                
            self.llm = ChatOpenAI(
                model=self.model_name,
                temperature=0.7,
                max_tokens=self.max_tokens,
                streaming=True,
                openai_api_key=api_key,
            )
            logger.info(f"OpenAI client initialized with model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            raise HTTPException(status_code=500, detail="Failed to initialize AI service")

    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        try:
            encoding = encoding_for_model(self.model_name)
            return len(encoding.encode(text))
        except Exception:
            # Fallback to approximate count
            return len(text.split()) * 1.3

    def _create_system_message(self) -> SystemMessage:
        """Create system message for the AI."""
        return SystemMessage(content="""You are a helpful AI assistant. 
        Provide clear, accurate, and helpful responses. 
        Be concise but thorough in your explanations.""")

    def _get_chat_messages(self, chat_id: UUID) -> List[BaseMessage]:
        """Get conversation history as LangChain messages."""
        messages = Message.get_chat_messages(self.db_session, chat_id)
        
        # Convert to LangChain format
        langchain_messages = [self._create_system_message()]
        
        for msg in messages:
            if msg.role == "user":
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                langchain_messages.append(SystemMessage(content=msg.content))
        
        return langchain_messages

    def _save_message(self, chat_id: UUID, role: str, content: str, tokens_used: Optional[str] = None) -> Message:
        """Save message to database."""
        message = Message(
            chat_id=chat_id,
            role=role,
            content=content,
            tokens_used=str(tokens_used) if tokens_used else None,
            llm_model=self.model_name,
        )
        self.db_session.add(message)
        self.db_session.commit()
        self.db_session.refresh(message)
        return message

    def _update_chat_title(self, chat: Chat, user_message: str) -> None:
        """Update chat title based on first user message."""
        if chat.title == "New Chat" and len(chat.messages) == 1:
            # Generate a title from the first message
            title = user_message[:50] + "..." if len(user_message) > 50 else user_message
            chat.update_title(title)
            self.db_session.commit()

    def create_chat(self, user_id: UUID, title: str = "New Chat") -> Chat:
        """Create a new chat session."""
        chat = Chat(
            user_id=user_id,
            title=title,
        )
        self.db_session.add(chat)
        self.db_session.commit()
        self.db_session.refresh(chat)
        logger.info(f"Created new chat {chat.id} for user {user_id}")
        return chat

    def get_chat(self, chat_id: UUID, user_id: UUID) -> Optional[Chat]:
        """Get chat by ID with user validation."""
        return Chat.get_by_id(self.db_session, chat_id, user_id)

    def get_user_chats(self, user_id: UUID, limit: int = 50, offset: int = 0) -> List[Chat]:
        """Get all chats for a user."""
        return Chat.get_user_chats(self.db_session, user_id, limit, offset)

    def update_chat_title(self, chat_id: UUID, user_id: UUID, new_title: str) -> Chat:
        """Update chat title."""
        chat = self.get_chat(chat_id, user_id)
        if not chat:
            raise HTTPException(status_code=404, detail="Chat not found")
        
        chat.update_title(new_title)
        self.db_session.commit()
        return chat

    def delete_chat(self, chat_id: UUID, user_id: UUID) -> bool:
        """Soft delete a chat."""
        chat = self.get_chat(chat_id, user_id)
        if not chat:
            raise HTTPException(status_code=404, detail="Chat not found")
        
        chat.deactivate()
        self.db_session.commit()
        logger.info(f"Deleted chat {chat_id} for user {user_id}")
        return True

    def get_chat_history(self, chat_id: UUID, user_id: UUID, limit: int = 100, offset: int = 0) -> tuple[Chat, List[Message], int]:
        """Get chat history with messages."""
        chat = self.get_chat(chat_id, user_id)
        if not chat:
            raise HTTPException(status_code=404, detail="Chat not found")
        
        messages = Message.get_chat_messages(self.db_session, chat_id, limit, offset)
        total_messages = Message.get_chat_messages_count(self.db_session, chat_id)
        
        return chat, messages, total_messages

    async def process_message(self, request: ChatMessageRequest, user_id: UUID) -> tuple[Chat, Message, str]:
        """Process a chat message and return response."""
        try:
            # Get or create chat
            if request.chat_id:
                chat = self.get_chat(request.chat_id, user_id)
                if not chat:
                    raise HTTPException(status_code=404, detail="Chat not found")
            else:
                chat = self.create_chat(user_id)

            # Save user message
            user_message = self._save_message(chat.id, "user", request.message)
            
            # Update chat title if it's the first message
            self._update_chat_title(chat, request.message)

            # Get conversation history
            messages = self._get_chat_messages(chat.id)
            
            # Generate AI response
            try:
                if hasattr(self, 'llm') and self.llm:
                    response = await self.llm.agenerate([messages])
                    ai_response = response.generations[0][0].text
                else:
                    # Mock response when OpenAI is not available
                    ai_response = f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly."
                
                # Count tokens
                tokens_used = self._count_tokens(request.message + ai_response)
                
                # Save AI response
                ai_message = self._save_message(chat.id, "assistant", ai_response, tokens_used)
                
                return chat, ai_message, ai_response
                
            except Exception as e:
                logger.error(f"Error generating AI response: {e}")
                # Return mock response on error
                ai_response = f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly."
                tokens_used = self._count_tokens(request.message + ai_response)
                ai_message = self._save_message(chat.id, "assistant", ai_response, tokens_used)
                return chat, ai_message, ai_response
                
        except Exception as e:
            logger.error(f"Database error in process_message: {e}")
            # Return mock response when database is not available
            import uuid
            mock_chat = type('MockChat', (), {
                'id': uuid.uuid4(),
                'title': 'Mock Chat',
                'user_id': user_id,
                'is_active': True,
                'created_at': datetime.now(UTC),
                'updated_at': datetime.now(UTC)
            })()
            
            mock_message = type('MockMessage', (), {
                'id': uuid.uuid4(),
                'chat_id': mock_chat.id,
                'role': 'assistant',
                'content': f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly.",
                'tokens_used': '150',
                'llm_model': 'gpt-3.5-turbo',
                'created_at': datetime.now(UTC)
            })()
            
            mock_response = f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly."
            
            return mock_chat, mock_message, mock_response

    async def stream_message(self, request: ChatMessageRequest, user_id: UUID) -> AsyncGenerator[StreamingChatResponse, None]:
        """Stream a chat message response."""
        # Get or create chat
        if request.chat_id:
            chat = self.get_chat(request.chat_id, user_id)
            if not chat:
                raise HTTPException(status_code=404, detail="Chat not found")
        else:
            chat = self.create_chat(user_id)

        # Save user message
        user_message = self._save_message(chat.id, "user", request.message)
        
        # Update chat title if it's the first message
        self._update_chat_title(chat, request.message)

        # Get conversation history
        messages = self._get_chat_messages(chat.id)
        
        # Stream AI response
        try:
            if hasattr(self, 'llm') and self.llm:
                full_response = ""
                async for chunk in self.llm.astream(messages):
                    if chunk.content:
                        full_response += chunk.content
                        yield StreamingChatResponse(
                            chat_id=chat.id,
                            content=chunk.content,
                            is_complete=False
                        )
            else:
                # Mock streaming response when OpenAI is not available
                mock_response = f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly."
                full_response = mock_response
                
                # Stream the response in chunks
                words = mock_response.split()
                for i, word in enumerate(words):
                    yield StreamingChatResponse(
                        chat_id=chat.id,
                        content=word + " ",
                        is_complete=False
                    )
                    # Small delay to simulate streaming
                    import asyncio
                    await asyncio.sleep(0.1)
            
            # Count tokens
            tokens_used = self._count_tokens(request.message + full_response)
            
            # Save AI response
            ai_message = self._save_message(chat.id, "assistant", full_response, tokens_used)
            
            # Send final chunk with metadata
            yield StreamingChatResponse(
                chat_id=chat.id,
                message_id=ai_message.id,
                content="",
                is_complete=True,
                tokens_used=str(tokens_used),
                llm_model=self.model_name
            )
            
        except Exception as e:
            logger.error(f"Error streaming AI response: {e}")
            # Return mock response on error
            mock_response = f"I'm a mock AI assistant. You said: '{request.message}'. This is a test response to demonstrate the chat functionality is working properly."
            tokens_used = self._count_tokens(request.message + mock_response)
            ai_message = self._save_message(chat.id, "assistant", mock_response, tokens_used)
            
            yield StreamingChatResponse(
                chat_id=chat.id,
                message_id=ai_message.id,
                content=mock_response,
                is_complete=True,
                tokens_used=str(tokens_used),
                llm_model=self.model_name
            )


def get_chat_service(db_session: Session = Depends(DatabaseSession)) -> ChatService:
    """Get chat service instance."""
    return ChatService(db_session)


def get_chat_service_simple() -> ChatService:
    """Get chat service instance with direct database connection."""
    from ..extensions.ext_db import db_engine
    from sqlmodel import Session
    
    engine = db_engine.get_engine()
    session = Session(engine)
    return ChatService(session)


def get_chat_service_dep() -> ChatService:
    """Get chat service instance for dependency injection."""
    from ..extensions.ext_db import db_engine
    from sqlmodel import Session
    
    try:
        engine = db_engine.get_engine()
        session = Session(engine)
        return ChatService(session)
    except Exception as e:
        # If database is not available, create a mock service for testing
        logger.warning(f"Database not available, creating mock service: {e}")
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        
        # Create an in-memory SQLite for testing
        test_engine = create_engine("sqlite:///:memory:")
        TestSession = sessionmaker(bind=test_engine)
        test_session = TestSession()
        
        # Create tables
        from ..entities.base import BaseEntity
        BaseEntity.metadata.create_all(test_engine)
        
        return ChatService(test_session) 